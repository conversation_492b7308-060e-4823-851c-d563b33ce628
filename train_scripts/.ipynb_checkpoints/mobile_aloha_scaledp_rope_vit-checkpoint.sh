#!/bin/bash
export OPENBLAS_NUM_THREADS=2
export GOTO_NUM_THREADS=2
export OMP_NUM_THREADS=2

backbone='/home/<USER>/tzb/zhumj/model_param/timm/vit_base_patch16_rope_reg1_gap_256.sbb_in1k'
backbone_class="vit_rope"
task_name=mobile_aloha_0505
chunk_size=50
batch_size=40
policy_class=DroidDiT
model_size=RDiT-GemmaGA
mobile_aloha=1
lr=1e-4          #1e-4 or 5e-5
lr_backbone=1e-5 #1e-4 or 5e-5
eta_min=0
seed=0
num_steps=60000
save_every=10000
is_use_sub_reason=0 #1 mean use sub step reason, 0 mean not use sub step reason
resume=0
use_constant=0

code_path=/home/<USER>/tzb/zhumj/code/scaledp/
ckpt_dir=/home/<USER>/tzb/zhumj/model_param/scaledp/vit/${task_name}_${model_size}_vit_base_${backbone_class}_lr_backbone_${lr_backbone}_chunk_${chunk_size}_wo_lang_bs_${batch_size}_constant_${use_constant}_eta_min_${eta_min}

mkdir $ckpt_dir
cp -rn ${code_path} $ckpt_dir
cp -rn ${code_path}/utils $ckpt_dir
cp -rn ${code_path}/policy_model $ckpt_dir
cp -rn ${code_path}/imitate_droid_episodes_ddp.py $ckpt_dir
cp -rn ${code_path} $ckpt_dir

python3 get_norm_stats_by_task.py --task_name $task_name --ckpt_dir $ckpt_dir

CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7, python3 imitate_droid_episodes_ddp.py \
--task_name $task_name \
--ckpt_dir $ckpt_dir \
--model_size ${model_size} \
--policy_class $policy_class \
--chunk_size $chunk_size \
--batch_size $batch_size \
--backbone $backbone \
--lr_backbone $lr_backbone \
--num_steps $num_steps \
--save_every $save_every \
--lr $lr \
--mobile_aloha $mobile_aloha \
--seed $seed \
--is_use_sub_reason $is_use_sub_reason \
--resume $resume \
--use_constant $use_constant \
--eta_min ${eta_min}